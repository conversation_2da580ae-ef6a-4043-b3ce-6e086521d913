"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { 
  ChevronRight, 
  ChevronDown, 
  Menu, 
  X, 
  Car,
  Home,
  Briefcase,
  Grid3X3,
  Sofa,
  Smartphone,
  Users,
  Zap
} from "lucide-react";

export default function ModernCategoryNavBar() {
  const router = useRouter();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeCategory, setActiveCategory] = useState(null);
  const [openMobileCategory, setOpenMobileCategory] = useState(null);

  // Helper function to create URL-friendly slugs
  const createSlug = (text) => {
    return text
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");
  };

  // Navigation handler
  const handleNavigation = (categoryName, subcategoryName, brandName) => {
    const categorySlug = createSlug(categoryName);

    if (brandName) {
      const brandSlug = createSlug(brandName);
      router.push(
        `/dubai/${categorySlug}/${
          subcategoryName ? createSlug(subcategoryName ?? "") : ""
        }/${brandSlug}`
      );
    } else if (subcategoryName) {
      const subcategorySlug = createSlug(subcategoryName);
      router.push(`/dubai/${categorySlug}/${subcategorySlug}`);
    } else {
      router.push(`/dubai/${categorySlug}`);
    }
    setIsMobileMenuOpen(false);
    setActiveCategory(null);
  };

  const categories = [
    {
      name: "Motors",
      icon: Car,
      isNew: true,
      color: "from-blue-500 to-blue-600",
      subcategories: [
        {
          title: "Vehicles",
          items: [
            "New Cars",
            "Used Cars", 
            "Rental Cars",
            "Motorcycles",
            "Boats",
            "Heavy Vehicles"
          ],
        },
        {
          title: "Services",
          items: [
            "Auto Parts",
            "Car Service",
            "Number Plates",
            "Insurance"
          ],
        },
      ],
      brands: [
        { name: "Toyota", count: "2.1k+" },
        { name: "Mercedes-Benz", count: "1.8k+" },
        { name: "BMW", count: "1.5k+" },
        { name: "Nissan", count: "1.2k+" },
        { name: "Ford", count: "950+" },
        { name: "Lexus", count: "780+" },
        { name: "Audi", count: "650+" },
        { name: "Honda", count: "540+" },
      ],
    },
    {
      name: "Property",
      icon: Home,
      isNew: true,
      color: "from-emerald-500 to-emerald-600",
      subcategories: [
        {
          title: "For Rent",
          items: [
            "Apartments",
            "Villas", 
            "Townhouses",
            "Penthouses",
            "Hotel Apartments",
            "Villa Compounds"
          ],
        },
        {
          title: "For Sale",
          items: [
            "Apartments",
            "Villas",
            "Townhouses", 
            "Penthouses",
            "Plots",
            "Commercial"
          ],
        },
      ],
    },
    {
      name: "Jobs",
      icon: Briefcase,
      color: "from-purple-500 to-purple-600",
      subcategories: [
        {
          title: "By Industry",
          items: [
            "Technology",
            "Healthcare",
            "Finance", 
            "Education",
            "Marketing",
            "Engineering"
          ],
        },
        {
          title: "By Level",
          items: [
            "Entry Level",
            "Mid Level",
            "Senior Level",
            "Executive",
            "Freelance"
          ],
        },
      ],
    },
    {
      name: "Electronics",
      icon: Zap,
      color: "from-orange-500 to-orange-600", 
      subcategories: [
        {
          title: "Computers",
          items: [
            "Laptops",
            "Desktops",
            "Gaming PCs",
            "Accessories"
          ],
        },
        {
          title: "Audio & Video",
          items: [
            "Headphones",
            "Speakers",
            "Cameras",
            "TVs"
          ],
        },
      ],
    },
    {
      name: "Furniture",
      icon: Sofa,
      color: "from-amber-500 to-amber-600",
      subcategories: [
        {
          title: "Living Room",
          items: [
            "Sofas",
            "Coffee Tables",
            "TV Units",
            "Chairs"
          ],
        },
        {
          title: "Bedroom",
          items: [
            "Beds",
            "Wardrobes", 
            "Dressers",
            "Nightstands"
          ],
        },
      ],
    },
    {
      name: "Mobiles",
      icon: Smartphone,
      color: "from-rose-500 to-rose-600",
      subcategories: [
        {
          title: "Phones",
          items: [
            "iPhone",
            "Samsung",
            "Huawei",
            "OnePlus"
          ],
        },
        {
          title: "Tablets", 
          items: [
            "iPad",
            "Galaxy Tab",
            "Surface",
            "Accessories"
          ],
        },
      ],
    },
    {
      name: "Services",
      icon: Users,
      color: "from-indigo-500 to-indigo-600",
      subcategories: [
        {
          title: "Home Services",
          items: [
            "Cleaning",
            "Maintenance",
            "Moving",
            "Security"
          ],
        },
        {
          title: "Personal",
          items: [
            "Tutoring",
            "Healthcare",
            "Beauty",
            "Fitness"
          ],
        },
      ],
    },
  ];

  return (
    <div className="bg-white shadow-sm border-b">
      {/* Desktop Navigation */}
      <div className="hidden lg:block">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center">
            {categories.map((category, index) => {
              const IconComponent = category.icon;
              return (
                <div
                  key={index}
                  className="relative group"
                  onMouseEnter={() => setActiveCategory(index)}
                  onMouseLeave={() => setActiveCategory(null)}
                >
                  <button
                    onClick={() => handleNavigation(category.name)}
                    className="flex items-center gap-3 px-6 py-4 text-gray-700 hover:text-gray-900 hover:bg-gray-50 transition-all duration-200 font-medium relative group"
                  >
                    <div className={`p-2 rounded-lg bg-gradient-to-r ${category.color} text-white group-hover:scale-110 transition-transform duration-200`}>
                      <IconComponent className="w-4 h-4" />
                    </div>
                    <div className="flex flex-col items-start">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-semibold">{category.name}</span>
                        {category.isNew && (
                          <span className="px-2 py-0.5 text-xs font-bold text-white bg-gradient-to-r from-red-500 to-pink-500 rounded-full animate-pulse">
                            NEW
                          </span>
                        )}
                      </div>
                      <span className="text-xs text-gray-500 mt-0.5">Browse all</span>
                    </div>
                  </button>

                  {/* Dropdown Menu */}
                  {activeCategory === index && (
                    <div className="absolute top-full left-0 w-[800px] bg-white shadow-2xl border border-gray-200 rounded-2xl p-6 z-50 transform opacity-0 translate-y-2 group-hover:opacity-100 group-hover:translate-y-0 transition-all duration-300">
                      <div className="grid grid-cols-12 gap-6">
                        {/* Left Side - Subcategories */}
                        <div className="col-span-5">
                          <div className="flex items-center gap-2 mb-4">
                            <div className={`p-2 rounded-lg bg-gradient-to-r ${category.color} text-white`}>
                              <IconComponent className="w-5 h-5" />
                            </div>
                            <h3 className="text-lg font-bold text-gray-900">{category.name}</h3>
                          </div>
                          
                          <div className="space-y-4">
                            {category.subcategories?.map((subcategory, subIndex) => (
                              <div key={subIndex} className="group/sub">
                                <h4 className="text-sm font-semibold text-gray-800 mb-2 pb-1 border-b border-gray-100">
                                  {subcategory.title}
                                </h4>
                                <div className="grid grid-cols-2 gap-1">
                                  {subcategory.items.map((item, itemIndex) => (
                                    <button
                                      key={itemIndex}
                                      onClick={() => handleNavigation(category.name, item)}
                                      className="text-left p-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors duration-200 flex items-center justify-between group"
                                    >
                                      <span>{item}</span>
                                      {item === "Rental Cars" && (
                                        <span className="ml-1 px-1.5 py-0.5 text-xs font-bold text-white bg-gradient-to-r from-green-500 to-emerald-500 rounded-full">
                                          HOT
                                        </span>
                                      )}
                                      <ChevronRight className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity" />
                                    </button>
                                  ))}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Right Side - Brands/Popular */}
                        <div className="col-span-7 border-l border-gray-100 pl-6">
                          {category.brands ? (
                            <>
                              <div className="flex items-center justify-between mb-4">
                                <h4 className="text-sm font-semibold text-gray-800">Popular Brands</h4>
                                <button
                                  onClick={() => handleNavigation(category.name, "Cars")}
                                  className="text-xs text-blue-600 hover:text-blue-800 font-medium flex items-center gap-1"
                                >
                                  View All <ChevronRight className="w-3 h-3" />
                                </button>
                              </div>
                              <div className="grid grid-cols-2 gap-2">
                                {category.brands.map((brand, brandIndex) => (
                                  <button
                                    key={brandIndex}
                                    onClick={() => handleNavigation(category.name, undefined, brand.name)}
                                    className="flex items-center justify-between p-3 border border-gray-200 rounded-xl hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 group"
                                  >
                                    <span className="font-medium text-gray-700 group-hover:text-blue-700">
                                      {brand.name}
                                    </span>
                                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full group-hover:bg-blue-100 group-hover:text-blue-600">
                                      {brand.count}
                                    </span>
                                  </button>
                                ))}
                              </div>
                            </>
                          ) : (
                            <div className="space-y-4">
                              <h4 className="text-sm font-semibold text-gray-800">Quick Access</h4>
                              <div className="grid grid-cols-1 gap-2">
                                {category.subcategories?.slice(0, 2).map((subcategory, subIndex) => (
                                  <div key={subIndex} className="p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl">
                                    <h5 className="font-medium text-gray-800 mb-2">{subcategory.title}</h5>
                                    <div className="flex flex-wrap gap-2">
                                      {subcategory.items.slice(0, 4).map((item, itemIndex) => (
                                        <button
                                          key={itemIndex}
                                          onClick={() => handleNavigation(category.name, item)}
                                          className="px-3 py-1 text-xs bg-white text-gray-600 hover:text-gray-900 hover:bg-gray-50 border border-gray-200 rounded-full transition-colors"
                                        >
                                          {item}
                                        </button>
                                      ))}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      <div className="lg:hidden">
        <div className="flex items-center justify-between px-4 py-3">
          <h2 className="text-lg font-bold text-gray-900">Categories</h2>
          <button
            onClick={() => setIsMobileMenuOpen(true)}
            className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl font-medium text-sm shadow-lg hover:shadow-xl transition-shadow"
          >
            <Menu className="w-4 h-4" />
            Browse All
          </button>
        </div>

        {/* Quick Categories Row */}
        <div className="px-4 pb-4">
          <div className="flex gap-3 overflow-x-auto scrollbar-hide">
            {categories.slice(0, 5).map((category, index) => {
              const IconComponent = category.icon;
              return (
                <button
                  key={index}
                  onClick={() => handleNavigation(category.name)}
                  className="flex-shrink-0 flex flex-col items-center gap-2 p-3 bg-white border border-gray-200 rounded-xl hover:shadow-md transition-shadow min-w-[80px]"
                >
                  <div className={`p-2 rounded-lg bg-gradient-to-r ${category.color} text-white`}>
                    <IconComponent className="w-5 h-5" />
                  </div>
                  <span className="text-xs font-medium text-gray-700 text-center leading-tight">
                    {category.name}
                  </span>
                  {category.isNew && (
                    <span className="px-1.5 py-0.5 text-xs font-bold text-white bg-red-500 rounded-full">
                      NEW
                    </span>
                  )}
                </button>
              );
            })}
          </div>
        </div>

        {/* Mobile Menu Overlay */}
        {isMobileMenuOpen && (
          <div className="fixed inset-0 z-50 bg-black bg-opacity-50">
            <div className="absolute inset-y-0 left-0 w-full max-w-sm bg-white shadow-xl">
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-blue-500 to-blue-600">
                <h3 className="text-lg font-bold text-white">All Categories</h3>
                <button
                  onClick={() => setIsMobileMenuOpen(false)}
                  className="p-2 text-white hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              {/* Categories List */}
              <div className="flex-1 overflow-y-auto p-4">
                <div className="space-y-2">
                  {categories.map((category, index) => {
                    const IconComponent = category.icon;
                    const isOpen = openMobileCategory === index;
                    
                    return (
                      <div key={index} className="border border-gray-200 rounded-xl overflow-hidden">
                        <button
                          onClick={() => {
                            if (isOpen) {
                              setOpenMobileCategory(null);
                            } else {
                              setOpenMobileCategory(index);
                            }
                          }}
                          className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 transition-colors"
                        >
                          <div className="flex items-center gap-3">
                            <div className={`p-2 rounded-lg bg-gradient-to-r ${category.color} text-white`}>
                              <IconComponent className="w-4 h-4" />
                            </div>
                            <div>
                              <span className="font-semibold text-gray-900">{category.name}</span>
                              {category.isNew && (
                                <span className="ml-2 px-2 py-0.5 text-xs font-bold text-white bg-red-500 rounded-full">
                                  NEW
                                </span>
                              )}
                            </div>
                          </div>
                          <ChevronDown
                            className={`w-5 h-5 text-gray-400 transition-transform ${
                              isOpen ? "rotate-180" : ""
                            }`}
                          />
                        </button>

                        {isOpen && (
                          <div className="border-t border-gray-100 bg-gray-50">
                            {category.subcategories?.map((subcategory, subIndex) => (
                              <div key={subIndex} className="p-4">
                                <h4 className="font-semibold text-gray-800 mb-3 text-sm">
                                  {subcategory.title}
                                </h4>
                                <div className="grid grid-cols-2 gap-2">
                                  {subcategory.items.map((item, itemIndex) => (
                                    <button
                                      key={itemIndex}
                                      onClick={() => handleNavigation(category.name, item)}
                                      className="text-left p-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-white rounded-lg transition-colors"
                                    >
                                      {item}
                                    </button>
                                  ))}
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      <style jsx global>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </div>
  );
}